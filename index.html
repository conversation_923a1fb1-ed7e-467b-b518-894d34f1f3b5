<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GamyDay Games - Dots and Boxes</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/canvas-confetti@1.9.2/dist/confetti.browser.min.js"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link
        href="https://fonts.googleapis.com/css2?family=Fredoka:wght@400;600;700&family=Inter:wght@400;500;700&display=swap"
        rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', sans-serif;
            margin: 0;
            overflow: hidden;
        }

        .font-fredoka {
            font-family: 'Fredoka', sans-serif;
        }

        @keyframes float {
            0%, 100% {
                transform: translateY(0);
            }
            50% {
                transform: translateY(-10px);
            }
        }

        .logo-float {
            animation: float 4s ease-in-out infinite;
        }

        @keyframes shine {
            0% {
                background-position: 200% 0;
            }
            100% {
                background-position: -200% 0;
            }
        }

        .progress-bar-shine::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(120deg, rgba(255, 255, 255, 0) 40%, rgba(255, 255, 255, 0.5) 50%, rgba(255, 255, 255, 0) 60%);
            background-size: 200% 100%;
            animation: shine 2s infinite linear;
            z-index: 1;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .fade-in-up {
            animation: fadeInUp 1s ease-out forwards;
        }

        @keyframes fadeOut {
            from {
                opacity: 1;
            }
            to {
                opacity: 0;
            }
        }

        .fade-out {
            animation: fadeOut 0.5s ease-out forwards;
        }

        .text-glow {
            text-shadow: 0 0 8px rgba(255, 255, 255, 0.3), 0 0 20px rgba(255, 255, 255, 0.1);
        }

        .glow-bar {
            box-shadow: 0 0 12px rgba(255, 186, 60, 0.5), 0 0 20px rgba(255, 115, 40, 0.3);
        }

        #loadingScreen {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 1000;
        }

        #gameUI {
            visibility: hidden;
            opacity: 0;
            transition: opacity 0.5s ease-in, visibility 0.5s;
        }

        #gameUI.active {
            visibility: visible;
            opacity: 1;
        }

        .dot {
            width: 16px;
            height: 16px;
            border-radius: 50%;
            background-color: #000;
            position: absolute;
            transform: translate(-50%, -50%);
            cursor: pointer;
            z-index: 10;
            transition: background-color 0.2s, transform 0.2s;
        }

        .dot:hover {
            background-color: #333;
            transform: translate(-50%, -50%) scale(1.2);
        }

        .dot.dragging {
            background-color: #666;
            transform: translate(-50%, -50%) scale(1.3);
        }

        .line {
            position: absolute;
            z-index: 5;
        }

        .line.player1 {
            background-color: #ef4444; /* red-500 */
        }

        .line.player2 {
            background-color: #3b82f6; /* blue-500 */
        }

        .preview-line {
            position: absolute;
            z-index: 8;
            pointer-events: none;
        }

        .preview-line.player1 {
            background-color: rgba(239, 68, 68, 0.4); /* red with opacity */
        }

        .preview-line.player2 {
            background-color: rgba(59, 130, 246, 0.4); /* blue with opacity */
        }

        .horizontal-line {
            height: 3px;
            width: 60px;
            transform: translateY(-50%);
        }

        .vertical-line {
            width: 3px;
            height: 60px;
            transform: translateX(-50%);
        }
        
        .box {
            position: absolute;
            width: 57px;
            height: 57px;
            border: none;
            transition: background-color 0.3s;
            border-radius: 4px;
        }

        .box.player1 {
            background-color: rgba(239, 68, 68, 0.7);
        }

        .box.player2 {
            background-color: rgba(59, 130, 246, 0.7);
        }

        /* Timer styles */
        .timer-warning {
            color: #f59e0b !important; /* amber-500 */
        }

        .timer-critical {
            color: #ef4444 !important; /* red-500 */
            animation: pulse 1s infinite;
        }

        .timer-bar-warning {
            background-color: #f59e0b !important; /* amber-500 */
        }

        .timer-bar-critical {
            background-color: #ef4444 !important; /* red-500 */
        }

        @keyframes pulse {
            0%, 100% {
                opacity: 1;
            }
            50% {
                opacity: 0.5;
            }
        }

        #modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.7);
            display: none;
            align-items: center;
            justify-content: center;
            z-index: 2000;
        }

        #modal.show {
            display: flex;
        }

        .modal-content {
            background: linear-gradient(135deg, #4c1d95, #1e1b4b);
            padding: 2rem;
            border-radius: 1rem;
            text-align: center;
            box-shadow: 0 0 20px rgba(255, 255, 255, 0.2);
            transform: scale(0.7);
            animation: modalPop 0.3s ease-out forwards;
        }

        @keyframes modalPop {
            to {
                transform: scale(1);
            }
        }

        #countdownTimer {
            animation: pulse 1s infinite;
            display: inline-block;
        }
    </style>
</head>
<body class="bg-gray-900">
    <div id="loadingScreen"
        class="fade-in-up flex flex-col items-center justify-center min-h-screen bg-gradient-to-br from-[#4c1d95] via-[#6b21a8] to-[#1e1b4b] p-6 text-white">
        <div class="mb-8 logo-float">
            <svg width="120" height="120" viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg">
                <defs>
                    <linearGradient id="logoGradient1" x1="0%" y1="0%" x2="100%" y2="100%">
                        <stop offset="0%" style="stop-color:#38bdf8" />
                        <stop offset="100%" style="stop-color:#6366f1" />
                    </linearGradient>
                    <linearGradient id="logoGradient2" x1="0%" y1="0%" x2="100%" y2="100%">
                        <stop offset="0%" style="stop-color:#fbbf24" />
                        <stop offset="100%" style="stop-color:#f97316" />
                    </linearGradient>
                    <linearGradient id="logoGradient3" x1="0%" y1="0%" x2="100%" y2="100%">
                        <stop offset="0%" style="stop-color:#ec4899" />
                        <stop offset="100%" style="stop-color:#d946ef" />
                    </linearGradient>
                </defs>
                <g transform="rotate(15 100 100)">
                    <path
                        d="M100 20 C144.18 20 180 55.82 180 100 C180 144.18 144.18 180 100 180 C55.82 180 20 144.18 20 100"
                        fill="none" stroke="url(#logoGradient1)" stroke-width="20" stroke-linecap="round" />
                    <path d="M100 20 C55.82 20 20 55.82 20 100 C20 144.18 55.82 180 100 180" fill="none"
                        stroke="url(#logoGradient2)" stroke-width="20" stroke-linecap="round"
                        transform="rotate(120 100 100)" />
                    <path d="M20 100 C20 55.82 55.82 20 100 20 C144.18 20 180 55.82 180 100" fill="none"
                        stroke="url(#logoGradient3)" stroke-width="20" stroke-linecap="round"
                        transform="rotate(240 100 100)" />
                    <circle cx="100" cy="100" r="25" fill="white" />
                </g>
            </svg>
        </div>
        <h1 class="font-fredoka text-4xl md:text-5xl font-bold text-center text-white tracking-wider text-glow mb-6">
            GamyDay Games
        </h1>
        <div class="w-full max-w-md mt-4 mb-8">
            <div class="h-4 bg-white/10 rounded-full overflow-hidden shadow-inner relative">
                <div id="progressBar"
                    class="h-full bg-gradient-to-r from-amber-400 to-orange-500 rounded-full transition-all duration-500 ease-out relative progress-bar-shine glow-bar"
                    style="width: 0%;"></div>
            </div>
            <p id="loadingMessage" class="text-center mt-4 text-sm text-gray-300 h-5 font-medium tracking-wide">
                Initializing...
            </p>
        </div>
        <div class="absolute bottom-6 text-center text-sm text-gray-400">
            powered by <span class="font-semibold text-white">Amrita</span>
        </div>
    </div>

    <div id="gameUI" class="flex flex-col lg:flex-row gap-8 items-start justify-center min-h-screen bg-gray-900 text-white p-4">
        <!-- Game Board -->
        <div class="bg-gray-800 rounded-lg shadow-lg p-8">
            <h1 class="font-fredoka text-3xl font-bold text-center mb-6 text-white">Dots and Boxes</h1>
            <div id="gameBoard" class="relative bg-gray-700 border-2 border-gray-600 rounded-lg" style="width: 480px; height: 480px;">
                <!-- Game elements will be generated here -->
            </div>
        </div>

        <!-- Sidebar -->
        <div class="bg-gray-800 rounded-lg shadow-lg p-6 min-w-[250px]">
            <h2 class="text-xl font-bold mb-4 text-white">Game Info</h2>

            <!-- Player Names -->
            <div class="mb-6">
                <div class="flex justify-between w-full mb-2">
                    <div class="text-lg font-semibold">
                        <span class="text-red-400">Player 1:</span> <span id="player1Name">Waiting...</span>
                    </div>
                </div>
                <div class="flex justify-between w-full">
                    <div class="text-lg font-semibold">
                        <span class="text-blue-400">Player 2:</span> <span id="player2Name">Waiting...</span>
                    </div>
                </div>
            </div>

            <!-- Current Turn -->
            <div class="mb-6">
                <h3 class="text-lg font-semibold mb-2 text-gray-300">Current Turn</h3>
                <div id="turnIndicator" class="text-center text-lg font-medium text-gray-400">
                    Waiting for players...
                </div>
            </div>

            <!-- Scoreboard -->
            <div class="mb-6">
                <h3 class="text-lg font-semibold mb-2 text-gray-300">Score</h3>
                <div class="space-y-2">
                    <div class="flex justify-between items-center">
                        <span class="text-red-400 font-semibold">Player 1:</span>
                        <span id="player1Score" class="text-xl font-bold text-white">0</span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-blue-400 font-semibold">Player 2:</span>
                        <span id="player2Score" class="text-xl font-bold text-white">0</span>
                    </div>
                </div>
            </div>

            <!-- Game Status -->
            <div id="gameStatus" class="text-center p-3 rounded-lg bg-gray-700">
                <span class="text-gray-300">Game in progress...</span>
            </div>
        </div>
    </div>

    <div id="modal">
        <div class="modal-content">
            <h2 id="modalTitle" class="font-fredoka text-3xl font-bold text-white mb-4"></h2>
            <p id="modalMessage" class="text-lg text-gray-200 mb-6"></p>
        </div>
    </div>

    <script>
        const progressBar = document.getElementById('progressBar');
        const loadingMessage = document.getElementById('loadingMessage');
        const loadingScreen = document.getElementById('loadingScreen');
        const gameUI = document.getElementById('gameUI');
        const gameBoard = document.getElementById('gameBoard');
        const player1Name = document.getElementById('player1Name');
        const player2Name = document.getElementById('player2Name');
        const turnIndicator = document.getElementById('turnIndicator');
        const modal = document.getElementById('modal');
        const modalTitle = document.getElementById('modalTitle');
        const modalMessage = document.getElementById('modalMessage');

        const messages = [
            "Warming up the engines...",
            "Connecting to the game servers...",
            "Looking for a game room...",
            "Loading awesome assets...",
            "Waiting for players...",
            "Assembling pixels...",
            "Almost there, get ready!",
            "Game ready!"
        ];

        let progress = 0;
        let messageIndex = 0;
        let ws;
        let myPlayerNumber = null;
        let currentTurn = null;
        let players = {};
        let gameStarted = false;
        let gameOver = false;
        let gameState = null;
        let loadingIntervals = {};

        // Game-specific variables
        let gridSize = 8;
        let spacing = 60;
        let offset = 30;
        let isDragging = false;
        let dragStartDot = null;
        let previewLine = null;
        let dots = [];

        window.onload = function () {
            const cleanUrl = window.location.origin + '/game-play/';
            window.history.replaceState({}, document.title, cleanUrl);
            initializeGameBoard();
            fetchWebSocketUrl();
            simulateLoading();
        };

        function simulateLoading() {
            const messageInterval = setInterval(() => {
                if (messageIndex < messages.length - 1) {
                    messageIndex++;
                    loadingMessage.innerText = messages[messageIndex];
                }
            }, 1200);

            const progressInterval = setInterval(() => {
                if (progress < 80) {
                    progress += Math.random() * 2.5;
                    progressBar.style.width = Math.min(progress, 80) + '%';
                }
            }, 100);

            loadingIntervals = {messageInterval, progressInterval};
        }

        async function fetchWebSocketUrl() {
            try {
                const response = await fetch('/game-play/room-service', {
                    method: 'GET',
                    credentials: 'include'
                });
                const data = await response.json();
                if (data.wsURL) {
                    connectWebSocket(data.wsURL);
                } else {
                    throw new Error('No WebSocket URL provided');
                }
            } catch (error) {
                console.error('Error fetching WebSocket URL:', error);
                loadingMessage.innerText = 'Error connecting to server...';
            }
        }

        function connectWebSocket(wsUrl) {
            ws = new WebSocket(wsUrl);

            ws.onopen = () => {
                console.log('WebSocket connected');
                loadingMessage.innerText = 'Connected! Waiting for players...';
            };

            ws.onmessage = (event) => {
                const data = JSON.parse(event.data);
                console.log('Received message:', data);
                handleWebSocketMessage(data);
            };

            ws.onclose = () => {
                console.log('WebSocket closed');
                showErrorModal('Connection lost. Please try again.');
            };

            ws.onerror = (error) => {
                console.error('WebSocket error:', error);
                showErrorModal('Connection error. Please try again.');
            };
        }

        function handleWebSocketMessage(data) {
            switch (data.type) {
                case 'game_state':
                    handleInitialGameState(data.state);
                    break;
                case 'game_start':
                    handleGameStart(data);
                    break;
                case 'game_update':
                    handleGameUpdate(data.state);
                    break;
                case 'personal_state':
                    handlePersonalState(data.state);
                    break;
                case 'player_joined':
                    handlePlayerJoined(data);
                    break;
                case 'player_left':
                    handlePlayerLeft(data);
                    break;
                case 'game_over':
                    handleGameOver(data);
                    break;
                case 'pong':
                    break;
                case 'error':
                    showErrorModal(data.message);
                    break;
                default:
                    console.warn('Unknown message type:', data.type);
            }
        }

        function handleGameStart(data) {
            console.log('Game starting:', data);
            gameStarted = true;
            gameOver = false;

            if (data.state) {
                handleGameUpdate(data.state);
            }

            if (loadingScreen.style.display !== 'none') {
                finishLoading();
            }
        }

        function handleInitialGameState(state) {
            console.log('Initial game state:', state);
            gameState = state;

            if (state.currentTurn) {
                currentTurn = state.currentTurn;
            }
            if (state.players) {
                players = state.players;
            }
            if (state.isGameOver !== undefined) {
                gameOver = state.isGameOver;
            }
            if (state.gridSize) {
                gridSize = state.gridSize;
            }

            console.log('Updated state - myPlayerNumber:', myPlayerNumber, 'currentTurn:', currentTurn, 'players:', players);

            updateGameBoard(state);
            updatePlayerNames();
            updateTurnIndicator();
        }

        function handleGameUpdate(state) {
            console.log('Game update:', state);
            gameState = state;

            if (state.currentTurn) {
                currentTurn = state.currentTurn;
            }
            if (state.players) {
                players = state.players;
            }
            if (state.isGameOver !== undefined) {
                gameOver = state.isGameOver;
            }

            updateGameBoard(state);
            updatePlayerNames();
            updateTurnIndicator();
        }

        function handlePersonalState(state) {
            if (state.playerNumber) {
                myPlayerNumber = state.playerNumber;
            }
            console.log('Personal state:', state);
            handleGameUpdate(state);
        }


        function handlePlayerJoined(data) {
            console.log('Player joined:', data.username);
            if (data.state) {
                handleGameUpdate(data.state);
            }
        }

        function handlePlayerLeft(data) {
            console.log('Player left:', data.username);
            if (data.state) {
                handleGameUpdate(data.state);
            }
        }

        function handleGameOver(data) {
            console.log('Game over:', data);
            gameOver = true;
            handleGameUpdate(data.state);
            showGameEndModal(data);
        }

        function updatePlayerNames() {
            const player1Text = players[1] || 'Waiting...';
            const player2Text = players[2] || 'Waiting...';

            player1Name.innerText = player1Text;
            player2Name.innerText = player2Text;

            console.log('Updated player names - 1:', player1Text, '2:', player2Text);
        }

        function updateTurnIndicator() {
            if (gameOver) {
                turnIndicator.innerText = 'Game Over';
                turnIndicator.className = 'text-center text-lg font-medium text-gray-400';
                return;
            }

            if (!currentTurn || !myPlayerNumber) {
                turnIndicator.innerText = 'Waiting for game to start...';
                turnIndicator.className = 'text-center text-lg font-medium text-gray-400';
                return;
            }

            const isMyTurn = currentTurn === myPlayerNumber;
            const currentPlayerName = players[currentTurn] || 'Unknown';

            turnIndicator.innerText = isMyTurn
                ? 'Your turn!'
                : `Waiting for ${currentPlayerName}'s turn...`;
            turnIndicator.className = `text-center text-lg font-medium ${isMyTurn ? 'text-blue-400' : 'text-orange-400'}`;

            console.log('Turn indicator updated - isMyTurn:', isMyTurn, 'currentTurn:', currentTurn, 'myPlayerNumber:', myPlayerNumber);
        }


        function finishLoading() {
            progress = 100;
            progressBar.style.width = '100%';
            loadingMessage.innerText = messages[messages.length - 1];

            if (loadingIntervals.messageInterval) {
                clearInterval(loadingIntervals.messageInterval);
            }
            if (loadingIntervals.progressInterval) {
                clearInterval(loadingIntervals.progressInterval);
            }

            setTimeout(() => {
                loadingScreen.classList.add('fade-out');
                setTimeout(() => {
                    loadingScreen.style.display = 'none';
                    document.body.style.overflow = 'auto';
                    gameUI.classList.add('active');
                }, 500);
            }, 1000);
        }

        function initializeGameBoard() {
            gameBoard.innerHTML = '';
            dots = [];

            // Create dots
            for (let row = 0; row < gridSize; row++) {
                for (let col = 0; col < gridSize; col++) {
                    const dot = document.createElement('div');
                    dot.className = 'dot';
                    dot.style.left = `${offset + col * spacing}px`;
                    dot.style.top = `${offset + row * spacing}px`;
                    dot.dataset.row = row;
                    dot.dataset.col = col;

                    // Add drag event listeners
                    dot.addEventListener('mousedown', (e) => startDrag(e, dot, row, col));

                    gameBoard.appendChild(dot);
                    dots.push({ element: dot, row, col });
                }
            }

            // Add global mouse events for dragging
            gameBoard.addEventListener('mousemove', (e) => onMouseMove(e));
            gameBoard.addEventListener('mouseup', (e) => endDrag(e));
            gameBoard.addEventListener('mouseleave', () => cancelDrag());
        }

        function startDrag(e, dot, row, col) {
            if (gameOver) return;

            // Check if it's the player's turn
            if (!myPlayerNumber || currentTurn !== myPlayerNumber) {
                console.log('Not your turn');
                return;
            }

            e.preventDefault();
            isDragging = true;
            dragStartDot = { element: dot, row, col };
            dot.classList.add('dragging');

            // Create preview line with current player's color
            previewLine = document.createElement('div');
            previewLine.className = `preview-line player${myPlayerNumber}`;
            gameBoard.appendChild(previewLine);

            updatePreviewLine(e);
        }

        function onMouseMove(e) {
            if (!isDragging || !previewLine) return;
            updatePreviewLine(e);
        }

        function updatePreviewLine(e) {
            if (!dragStartDot || !previewLine) return;

            const rect = gameBoard.getBoundingClientRect();
            const mouseX = e.clientX - rect.left;
            const mouseY = e.clientY - rect.top;

            const startX = offset + dragStartDot.col * spacing;
            const startY = offset + dragStartDot.row * spacing;

            const deltaX = mouseX - startX;
            const deltaY = mouseY - startY;
            const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);

            if (distance < 5) {
                previewLine.style.display = 'none';
                return;
            }

            previewLine.style.display = 'block';

            // Calculate line position and rotation
            const angle = Math.atan2(deltaY, deltaX);
            const centerX = startX + deltaX / 2;
            const centerY = startY + deltaY / 2;

            previewLine.style.left = `${centerX}px`;
            previewLine.style.top = `${centerY}px`;
            previewLine.style.width = `${distance}px`;
            previewLine.style.height = '3px';
            previewLine.style.transform = `translate(-50%, -50%) rotate(${angle}rad)`;
            previewLine.style.transformOrigin = 'center';
        }

        function endDrag(e) {
            if (!isDragging) return;

            const targetDot = getDotAtPosition(e);

            if (targetDot && isValidConnection(dragStartDot, targetDot)) {
                sendLineAction(dragStartDot, targetDot);
            }

            cancelDrag();
        }

        function cancelDrag() {
            if (dragStartDot) {
                dragStartDot.element.classList.remove('dragging');
            }

            if (previewLine) {
                previewLine.remove();
                previewLine = null;
            }

            isDragging = false;
            dragStartDot = null;
        }

        function getDotAtPosition(e) {
            const rect = gameBoard.getBoundingClientRect();
            const mouseX = e.clientX - rect.left;
            const mouseY = e.clientY - rect.top;

            for (const dot of dots) {
                const dotX = offset + dot.col * spacing;
                const dotY = offset + dot.row * spacing;
                const distance = Math.sqrt((mouseX - dotX) ** 2 + (mouseY - dotY) ** 2);

                if (distance <= 15) { // Within 15px of dot center
                    return dot;
                }
            }
            return null;
        }

        function isValidConnection(startDot, endDot) {
            if (!startDot || !endDot || startDot === endDot) return false;

            const rowDiff = Math.abs(startDot.row - endDot.row);
            const colDiff = Math.abs(startDot.col - endDot.col);

            // Must be adjacent (horizontally or vertically, not diagonally)
            const isAdjacent = (rowDiff === 1 && colDiff === 0) || (rowDiff === 0 && colDiff === 1);

            if (!isAdjacent) return false;

            // Check if line already exists in game state
            if (gameState && gameState.lines) {
                const lineId = getLineId(startDot, endDot);
                return !gameState.lines[lineId];
            }

            return true;
        }

        function sendLineAction(startDot, endDot) {
            if (ws && ws.readyState === WebSocket.OPEN) {
                const action = {
                    type: 'game_action',
                    action: {
                        startRow: startDot.row,
                        startCol: startDot.col,
                        endRow: endDot.row,
                        endCol: endDot.col
                    }
                };
                console.log('Sending line action:', action);
                ws.send(JSON.stringify(action));
            } else {
                console.error('WebSocket not ready:', ws ? ws.readyState : 'null');
            }
        }

        function getLineId(startDot, endDot) {
            const minRow = Math.min(startDot.row, endDot.row);
            const maxRow = Math.max(startDot.row, endDot.row);
            const minCol = Math.min(startDot.col, endDot.col);
            const maxCol = Math.max(startDot.col, endDot.col);

            if (minRow === maxRow) {
                // Horizontal line
                return `horizontal-${minRow}-${minCol}`;
            } else {
                // Vertical line
                return `vertical-${minRow}-${minCol}`;
            }
        }


        function updateGameBoard(state) {
            if (!state) return;

            // Clear existing lines and boxes
            const existingLines = gameBoard.querySelectorAll('.line');
            const existingBoxes = gameBoard.querySelectorAll('.box');
            existingLines.forEach(line => line.remove());
            existingBoxes.forEach(box => box.remove());

            // Draw lines
            if (state.lines) {
                for (const lineId in state.lines) {
                    if (state.lines[lineId]) {
                        drawLine(lineId, state);
                    }
                }
            }

            // Draw boxes
            if (state.boxes) {
                state.boxes.forEach(box => {
                    drawBox(box);
                });
            }

            // Update scores
            if (state.scores) {
                document.getElementById('player1Score').textContent = state.scores[1] || 0;
                document.getElementById('player2Score').textContent = state.scores[2] || 0;
            }
        }

        function drawLine(lineId, state) {
            const parts = lineId.split('-');
            const type = parts[0]; // 'horizontal' or 'vertical'
            const row = parseInt(parts[1]);
            const col = parseInt(parts[2]);

            const line = document.createElement('div');

            // Determine which player drew this line
            let playerNumber = 1; // Default
            if (state.currentTurn && state.currentTurn !== myPlayerNumber) {
                playerNumber = state.currentTurn === 1 ? 2 : 1; // The other player
            } else if (myPlayerNumber) {
                playerNumber = myPlayerNumber;
            }

            line.className = `line player${playerNumber}`;

            if (type === 'horizontal') {
                line.classList.add('horizontal-line');
                line.style.left = `${offset + col * spacing + 8}px`;
                line.style.top = `${offset + row * spacing}px`;
            } else {
                line.classList.add('vertical-line');
                line.style.left = `${offset + col * spacing}px`;
                line.style.top = `${offset + row * spacing + 8}px`;
            }

            gameBoard.appendChild(line);
        }

        function drawBox(box) {
            const boxElement = document.createElement('div');
            boxElement.className = 'box';

            if (box.Owner > 0) {
                boxElement.classList.add(`player${box.Owner}`);
            }

            const lineThickness = 3;
            const boxX = offset + box.Col * spacing + lineThickness / 2;
            const boxY = offset + box.Row * spacing + lineThickness / 2;
            boxElement.style.left = `${boxX}px`;
            boxElement.style.top = `${boxY}px`;
            boxElement.dataset.row = box.Row;
            boxElement.dataset.col = box.Col;

            gameBoard.appendChild(boxElement);
        }


        function showGameEndModal(data) {
            const winner = data.winner;
            const winnerName = winner ? players[winner] : null;
            const isPrematureEnd = !winner;

            if (isPrematureEnd) {
                modalTitle.innerText = 'Game Ended';
                modalMessage.innerText = 'A player left the game early. The game has ended with no winner.';
            } else {
                modalTitle.innerText = winner ? `${winnerName} Wins!` : 'Draw!';
                modalMessage.innerText = winner
                    ? `🎉 Congratulations to ${winnerName} for winning the game!`
                    : 'The game ended in a draw. Want to play again?';
            }

            modal.classList.add('show');

            if (winner) {
                confetti({
                    particleCount: 120,
                    spread: 90,
                    origin: {y: 0.6}
                });
            }

            redirect();
        }

        function showErrorModal(message) {
            modalTitle.innerText = 'Game Error';
            modalMessage.innerText = message;
            modal.classList.add('show');

            redirect();
        }

        function redirect() {
            fetch('/game-play/get-return-url', {credentials: 'include'})
                .then(res => res.json())
                .then(({returnUrl}) => {
                    let countdown = 5;
                    modalMessage.innerHTML += `<br><br><span id="countdownTimer" class="text-sm text-amber-300 font-medium">Redirecting in ${countdown} seconds...</span>`;

                    const timer = setInterval(() => {
                        countdown--;
                        const timerEl = document.getElementById('countdownTimer');
                        if (timerEl) {
                            if (countdown <= 0) {
                                clearInterval(timer);
                                window.location.href = returnUrl;
                            } else {
                                timerEl.innerText = `Redirecting in ${countdown} seconds...`;
                            }
                        }
                    }, 1000);
                })
                .catch(err => {
                    console.error('Failed to fetch return URL:', err);
                });
        }


        // Ping to keep connection alive
        setInterval(() => {
            if (ws && ws.readyState === WebSocket.OPEN) {
                ws.send(JSON.stringify({type: 'ping'}));
            }
        }, 30000);
    </script>
</body>
</html>
