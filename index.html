<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dots and Boxes Game</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .dot {
            width: 16px;
            height: 16px;
            border-radius: 50%;
            background-color: #000;
            position: absolute;
            transform: translate(-50%, -50%);
            cursor: pointer;
            z-index: 10;
            transition: background-color 0.2s, transform 0.2s;
        }

        .dot:hover {
            background-color: #333;
            transform: translate(-50%, -50%) scale(1.2);
        }

        .dot.dragging {
            background-color: #666;
            transform: translate(-50%, -50%) scale(1.3);
        }

        .line {
            position: absolute;
            z-index: 5;
        }

        .line.player1 {
            background-color: #ef4444; /* red-500 */
        }

        .line.player2 {
            background-color: #3b82f6; /* blue-500 */
        }

        .preview-line {
            position: absolute;
            z-index: 8;
            pointer-events: none;
        }

        .preview-line.player1 {
            background-color: rgba(239, 68, 68, 0.4); /* red with opacity */
        }

        .preview-line.player2 {
            background-color: rgba(59, 130, 246, 0.4); /* blue with opacity */
        }

        .horizontal-line {
            height: 3px;
            width: 60px;
            transform: translateY(-50%);
        }

        .vertical-line {
            width: 3px;
            height: 60px;
            transform: translateX(-50%);
        }
        
        .box {
            position: absolute;
            width: 57px;
            height: 57px;
            border: none;
            transition: background-color 0.3s;
            border-radius: 4px;
        }

        .box.player1 {
            background-color: rgba(239, 68, 68, 0.7);
        }

        .box.player2 {
            background-color: rgba(59, 130, 246, 0.7);
        }

        /* Timer styles */
        .timer-warning {
            color: #f59e0b !important; /* amber-500 */
        }

        .timer-critical {
            color: #ef4444 !important; /* red-500 */
            animation: pulse 1s infinite;
        }

        .timer-bar-warning {
            background-color: #f59e0b !important; /* amber-500 */
        }

        .timer-bar-critical {
            background-color: #ef4444 !important; /* red-500 */
        }

        @keyframes pulse {
            0%, 100% {
                opacity: 1;
            }
            50% {
                opacity: 0.5;
            }
        }
    </style>
</head>
<body class="bg-gray-100 min-h-screen flex items-center justify-center p-4">
    <div class="flex flex-col lg:flex-row gap-8 items-start">
        <!-- Game Board -->
        <div class="bg-white rounded-lg shadow-lg p-8">
            <h1 class="text-3xl font-bold text-center mb-6 text-gray-800">Dots and Boxes</h1>
            <div id="gameBoard" class="relative bg-gray-50 border-2 border-gray-200 rounded-lg" style="width: 480px; height: 480px;">
                <!-- Game elements will be generated here -->
            </div>
        </div>
        
        <!-- Sidebar -->
        <div class="bg-white rounded-lg shadow-lg p-6 min-w-[250px]">
            <h2 class="text-xl font-bold mb-4 text-gray-800">Game Info</h2>
            
            <!-- Current Turn -->
            <div class="mb-6">
                <h3 class="text-lg font-semibold mb-2 text-gray-700">Current Turn</h3>
                <div id="currentTurn" class="flex items-center gap-2">
                    <span class="text-2xl">→</span>
                    <span class="font-bold text-red-500">Player 1</span>
                </div>
            </div>

            <!-- Timer -->
            <div class="mb-6">
                <h3 class="text-lg font-semibold mb-2 text-gray-700">Turn Timer</h3>
                <div class="text-center">
                    <div id="timerDisplay" class="text-4xl font-bold text-gray-800 mb-2">30</div>
                    <div class="w-full bg-gray-200 rounded-full h-3">
                        <div id="timerBar" class="bg-green-500 h-3 rounded-full transition-all duration-1000" style="width: 100%"></div>
                    </div>
                    <div id="timerStatus" class="text-sm text-gray-600 mt-2">Timer active</div>
                </div>
            </div>
            
            <!-- Scoreboard -->
            <div class="mb-6">
                <h3 class="text-lg font-semibold mb-2 text-gray-700">Score</h3>
                <div class="space-y-2">
                    <div class="flex justify-between items-center">
                        <span class="text-red-500 font-semibold">Player 1:</span>
                        <span id="player1Score" class="text-xl font-bold">0</span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-blue-500 font-semibold">Player 2:</span>
                        <span id="player2Score" class="text-xl font-bold">0</span>
                    </div>
                </div>
            </div>
            
            <!-- Turn Count -->
            <div class="mb-6">
                <h3 class="text-lg font-semibold mb-2 text-gray-700">Turn Count</h3>
                <div id="turnCount" class="text-xl font-bold text-gray-600">1</div>
            </div>
            
            <!-- Game Status -->
            <div id="gameStatus" class="text-center p-3 rounded-lg bg-gray-100">
                <span class="text-gray-600">Game in progress...</span>
            </div>
            
            <!-- Reset Button -->
            <button id="resetBtn" class="w-full mt-4 bg-gray-500 hover:bg-gray-600 text-white font-bold py-2 px-4 rounded transition-colors">
                New Game
            </button>
        </div>
    </div>

    <script>
        class DotsAndBoxesGame {
            constructor() {
                this.gridSize = 8;
                this.currentPlayer = 1;
                this.scores = { 1: 0, 2: 0 };
                this.turnCount = 1;
                this.gameBoard = document.getElementById('gameBoard');
                this.lines = new Set();
                this.boxes = [];
                this.gameEnded = false;
                this.dots = [];

                // Drag state
                this.isDragging = false;
                this.dragStartDot = null;
                this.previewLine = null;
                this.spacing = 60;
                this.offset = 30;

                // Timer properties
                this.timerDuration = 30; // 30 seconds per turn
                this.currentTime = this.timerDuration;
                this.timerInterval = null;
                this.timerPaused = false;

                this.initializeGame();
                this.setupEventListeners();
            }
            
            initializeGame() {
                this.gameBoard.innerHTML = '';
                this.lines.clear();
                this.boxes = [];
                this.dots = [];
                this.currentPlayer = 1;
                this.scores = { 1: 0, 2: 0 };
                this.turnCount = 1;
                this.gameEnded = false;
                this.isDragging = false;
                this.dragStartDot = null;
                this.previewLine = null;

                // Reset timer
                this.stopTimer();
                this.currentTime = this.timerDuration;
                this.timerPaused = false;

                this.createDots();
                this.createBoxes();
                this.updateUI();
                this.startTimer();
            }

            createDots() {
                for (let row = 0; row < this.gridSize; row++) {
                    for (let col = 0; col < this.gridSize; col++) {
                        const dot = document.createElement('div');
                        dot.className = 'dot';
                        dot.style.left = `${this.offset + col * this.spacing}px`;
                        dot.style.top = `${this.offset + row * this.spacing}px`;
                        dot.dataset.row = row;
                        dot.dataset.col = col;

                        // Add drag event listeners
                        dot.addEventListener('mousedown', (e) => this.startDrag(e, dot, row, col));

                        this.gameBoard.appendChild(dot);
                        this.dots.push({ element: dot, row, col });
                    }
                }

                // Add global mouse events for dragging
                this.gameBoard.addEventListener('mousemove', (e) => this.onMouseMove(e));
                this.gameBoard.addEventListener('mouseup', (e) => this.endDrag(e));
                this.gameBoard.addEventListener('mouseleave', () => this.cancelDrag());
            }
            
            startTimer() {
                if (this.gameEnded || this.timerInterval) return;

                this.timerInterval = setInterval(() => {
                    if (!this.timerPaused && !this.gameEnded) {
                        this.currentTime--;
                        this.updateTimerDisplay();

                        if (this.currentTime <= 0) {
                            this.handleTimerExpired();
                        }
                    }
                }, 1000);
            }

            stopTimer() {
                if (this.timerInterval) {
                    clearInterval(this.timerInterval);
                    this.timerInterval = null;
                }
            }

            pauseTimer() {
                this.timerPaused = true;
            }

            resumeTimer() {
                this.timerPaused = false;
            }

            resetTimer() {
                this.currentTime = this.timerDuration;
                this.updateTimerDisplay();
            }

            handleTimerExpired() {
                this.stopTimer();
                // End the game due to timeout - the other player wins
                this.endGameDueToTimeout();
            }

            endGameDueToTimeout() {
                if (this.gameEnded) return; // Prevent double execution

                this.gameEnded = true;
                const winner = this.currentPlayer === 1 ? 2 : 1; // Other player wins

                // Log timeout event for debugging
                console.log(`Player ${this.currentPlayer} timed out. Player ${winner} wins.`);

                this.endGame(winner, 'timeout');
            }



            endGame(winner, reason = 'normal') {
                this.gameEnded = true;
                this.stopTimer();

                const gameStatus = document.getElementById('gameStatus');
                const timerStatus = document.getElementById('timerStatus');

                let statusMessage = '';
                let statusClass = '';

                if (reason === 'timeout') {
                    statusMessage = `
                        <span class="font-bold ${winner === 1 ? 'text-red-500' : 'text-blue-500'}">
                            Player ${winner} Wins!
                        </span>
                        <div class="text-sm text-gray-600 mt-1">Player ${this.currentPlayer} ran out of time</div>
                    `;
                    statusClass = 'text-center p-3 rounded-lg bg-orange-100';
                    timerStatus.textContent = 'Game ended - timeout';
                } else {
                    // Normal game completion
                    if (winner) {
                        statusMessage = `
                            <span class="font-bold ${winner === 1 ? 'text-red-500' : 'text-blue-500'}">
                                Player ${winner} Wins!
                            </span>
                        `;
                        statusClass = 'text-center p-3 rounded-lg bg-green-100';
                    } else {
                        statusMessage = '<span class="font-bold text-gray-600">It\'s a Tie!</span>';
                        statusClass = 'text-center p-3 rounded-lg bg-yellow-100';
                    }
                    timerStatus.textContent = 'Game finished';
                }

                gameStatus.innerHTML = statusMessage;
                gameStatus.className = statusClass;
            }

            updateTimerDisplay() {
                const timerDisplay = document.getElementById('timerDisplay');
                const timerBar = document.getElementById('timerBar');
                const timerStatus = document.getElementById('timerStatus');

                timerDisplay.textContent = this.currentTime;

                // Update progress bar
                const percentage = (this.currentTime / this.timerDuration) * 100;
                timerBar.style.width = `${percentage}%`;

                // Update colors based on time remaining
                timerDisplay.className = 'text-4xl font-bold';
                timerBar.className = 'h-3 rounded-full transition-all duration-1000';

                if (this.currentTime <= 5) {
                    timerDisplay.classList.add('timer-critical');
                    timerBar.classList.add('timer-bar-critical');
                    timerStatus.textContent = 'Time running out!';
                } else if (this.currentTime <= 10) {
                    timerDisplay.classList.add('timer-warning');
                    timerBar.classList.add('timer-bar-warning');
                    timerStatus.textContent = 'Hurry up!';
                } else {
                    timerDisplay.classList.add('text-gray-800');
                    timerBar.classList.add('bg-green-500');
                    timerStatus.textContent = 'Timer active';
                }
            }

            startDrag(e, dot, row, col) {
                if (this.gameEnded) return;

                e.preventDefault();
                this.isDragging = true;
                this.dragStartDot = { element: dot, row, col };
                dot.classList.add('dragging');

                // Pause timer during drag
                this.pauseTimer();

                // Create preview line with current player's color
                this.previewLine = document.createElement('div');
                this.previewLine.className = `preview-line player${this.currentPlayer}`;
                this.gameBoard.appendChild(this.previewLine);

                this.updatePreviewLine(e);
            }

            onMouseMove(e) {
                if (!this.isDragging || !this.previewLine) return;
                this.updatePreviewLine(e);
            }

            updatePreviewLine(e) {
                if (!this.dragStartDot || !this.previewLine) return;

                const rect = this.gameBoard.getBoundingClientRect();
                const mouseX = e.clientX - rect.left;
                const mouseY = e.clientY - rect.top;

                const startX = this.offset + this.dragStartDot.col * this.spacing;
                const startY = this.offset + this.dragStartDot.row * this.spacing;

                const deltaX = mouseX - startX;
                const deltaY = mouseY - startY;
                const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);

                if (distance < 5) {
                    this.previewLine.style.display = 'none';
                    return;
                }

                this.previewLine.style.display = 'block';

                // Calculate line position and rotation
                const angle = Math.atan2(deltaY, deltaX);
                const centerX = startX + deltaX / 2;
                const centerY = startY + deltaY / 2;

                this.previewLine.style.left = `${centerX}px`;
                this.previewLine.style.top = `${centerY}px`;
                this.previewLine.style.width = `${distance}px`;
                this.previewLine.style.height = '3px';
                this.previewLine.style.transform = `translate(-50%, -50%) rotate(${angle}rad)`;
                this.previewLine.style.transformOrigin = 'center';
            }
            
            endDrag(e) {
                if (!this.isDragging) return;

                const targetDot = this.getDotAtPosition(e);

                if (targetDot && this.isValidConnection(this.dragStartDot, targetDot)) {
                    this.createLine(this.dragStartDot, targetDot);
                } else {
                    // Resume timer if no valid line was created
                    this.resumeTimer();
                }

                this.cancelDrag();
            }

            cancelDrag() {
                if (this.dragStartDot) {
                    this.dragStartDot.element.classList.remove('dragging');
                }

                if (this.previewLine) {
                    this.previewLine.remove();
                    this.previewLine = null;
                }

                this.isDragging = false;
                this.dragStartDot = null;

                // Resume timer when drag is cancelled
                if (!this.gameEnded) {
                    this.resumeTimer();
                }
            }

            getDotAtPosition(e) {
                const rect = this.gameBoard.getBoundingClientRect();
                const mouseX = e.clientX - rect.left;
                const mouseY = e.clientY - rect.top;

                for (const dot of this.dots) {
                    const dotX = this.offset + dot.col * this.spacing;
                    const dotY = this.offset + dot.row * this.spacing;
                    const distance = Math.sqrt((mouseX - dotX) ** 2 + (mouseY - dotY) ** 2);

                    if (distance <= 15) { // Within 15px of dot center
                        return dot;
                    }
                }
                return null;
            }

            isValidConnection(startDot, endDot) {
                if (!startDot || !endDot || startDot === endDot) return false;

                const rowDiff = Math.abs(startDot.row - endDot.row);
                const colDiff = Math.abs(startDot.col - endDot.col);

                // Must be adjacent (horizontally or vertically, not diagonally)
                const isAdjacent = (rowDiff === 1 && colDiff === 0) || (rowDiff === 0 && colDiff === 1);

                if (!isAdjacent) return false;

                // Check if line already exists
                const lineId = this.getLineId(startDot, endDot);
                return !this.lines.has(lineId);
            }

            getLineId(dot1, dot2) {
                const minRow = Math.min(dot1.row, dot2.row);
                const maxRow = Math.max(dot1.row, dot2.row);
                const minCol = Math.min(dot1.col, dot2.col);
                const maxCol = Math.max(dot1.col, dot2.col);

                if (minRow === maxRow) {
                    // Horizontal line
                    return `horizontal-${minRow}-${minCol}`;
                } else {
                    // Vertical line
                    return `vertical-${minRow}-${minCol}`;
                }
            }

            createBoxes() {
                for (let row = 0; row < this.gridSize - 1; row++) {
                    for (let col = 0; col < this.gridSize - 1; col++) {
                        const box = document.createElement('div');
                        box.className = 'box';
                        // Position box to align perfectly with the line boundaries
                        // Following the same logic as index2.html: position + lineThickness/2
                        const lineThickness = 3;
                        const boxX = this.offset + col * this.spacing + lineThickness / 2;
                        const boxY = this.offset + row * this.spacing + lineThickness / 2;
                        box.style.left = `${boxX}px`;
                        box.style.top = `${boxY}px`;
                        box.dataset.row = row;
                        box.dataset.col = col;
                        this.gameBoard.appendChild(box);
                        this.boxes.push({ row, col, owner: null, element: box });
                    }
                }
            }

            createLine(startDot, endDot) {
                const lineId = this.getLineId(startDot, endDot);
                this.lines.add(lineId);

                // Create visual line element with current player's color
                const line = document.createElement('div');
                line.className = `line player${this.currentPlayer}`;

                const startX = this.offset + startDot.col * this.spacing;
                const startY = this.offset + startDot.row * this.spacing;
                const endX = this.offset + endDot.col * this.spacing;
                const endY = this.offset + endDot.row * this.spacing;

                if (startDot.row === endDot.row) {
                    // Horizontal line
                    line.classList.add('horizontal-line');
                    // Position line to align with box boundaries
                    // Offset by dot radius (8px) to center the line between dots
                    line.style.left = `${Math.min(startX, endX) + 8}px`;
                    line.style.top = `${startY}px`;
                } else {
                    // Vertical line
                    line.classList.add('vertical-line');
                    // Position line to align with box boundaries
                    // Offset by dot radius (8px) to center the line between dots
                    line.style.left = `${startX}px`;
                    line.style.top = `${Math.min(startY, endY) + 8}px`;
                }

                this.gameBoard.appendChild(line);

                const completedBoxes = this.checkCompletedBoxes();

                if (completedBoxes.length > 0) {
                    // Player gets another turn for completing boxes
                    this.scores[this.currentPlayer] += completedBoxes.length;
                    completedBoxes.forEach(box => {
                        box.owner = this.currentPlayer;
                        box.element.classList.add(`player${this.currentPlayer}`);
                    });
                    // Reset timer to 30 seconds and continue current player's turn
                    this.resetTimer();
                    this.resumeTimer();
                } else {
                    // Switch turns immediately
                    this.switchTurn();
                }

                this.updateUI();
                this.checkGameEnd();
            }

            switchTurn() {
                this.stopTimer();
                this.currentPlayer = this.currentPlayer === 1 ? 2 : 1;
                this.turnCount++;
                this.resetTimer();
                if (!this.gameEnded) {
                    this.startTimer();
                }
            }

            checkCompletedBoxes() {
                const newlyCompleted = [];

                this.boxes.forEach(box => {
                    if (box.owner === null && this.isBoxComplete(box.row, box.col)) {
                        newlyCompleted.push(box);
                    }
                });

                return newlyCompleted;
            }

            isBoxComplete(row, col) {
                const top = `horizontal-${row}-${col}`;
                const bottom = `horizontal-${row + 1}-${col}`;
                const left = `vertical-${row}-${col}`;
                const right = `vertical-${row}-${col + 1}`;

                return this.lines.has(top) && this.lines.has(bottom) &&
                       this.lines.has(left) && this.lines.has(right);
            }

            updateUI() {
                // Update current turn
                const currentTurnElement = document.getElementById('currentTurn');
                if (this.gameEnded) {
                    currentTurnElement.innerHTML = `
                        <span class="text-2xl">🏁</span>
                        <span class="font-bold text-gray-500">Game Over</span>
                    `;
                } else {
                    currentTurnElement.innerHTML = `
                        <span class="text-2xl">→</span>
                        <span class="font-bold ${this.currentPlayer === 1 ? 'text-red-500' : 'text-blue-500'}">
                            Player ${this.currentPlayer}
                        </span>
                    `;
                }

                // Update scores
                document.getElementById('player1Score').textContent = this.scores[1];
                document.getElementById('player2Score').textContent = this.scores[2];

                // Update turn count
                document.getElementById('turnCount').textContent = this.turnCount;

                // Update timer display
                this.updateTimerDisplay();

            }

            checkGameEnd() {
                const totalBoxes = (this.gridSize - 1) * (this.gridSize - 1);
                const completedBoxes = this.scores[1] + this.scores[2];

                if (completedBoxes === totalBoxes) {
                    const winner = this.scores[1] > this.scores[2] ? 1 :
                                  this.scores[2] > this.scores[1] ? 2 : null;
                    this.endGame(winner, 'normal');
                }
            }

            setupEventListeners() {
                document.getElementById('resetBtn').addEventListener('click', () => {
                    this.initializeGame();
                    document.getElementById('gameStatus').innerHTML = '<span class="text-gray-600">Game in progress...</span>';
                    document.getElementById('gameStatus').className = 'text-center p-3 rounded-lg bg-gray-100';
                    document.getElementById('timerStatus').textContent = 'Timer active';
                });



                // Handle page visibility changes to pause/resume timer
                document.addEventListener('visibilitychange', () => {
                    if (document.hidden) {
                        this.pauseTimer();
                    } else if (!this.gameEnded && !this.isDragging) {
                        this.resumeTimer();
                    }
                });
            }


        }

        // Initialize the game when the page loads
        document.addEventListener('DOMContentLoaded', () => {
            new DotsAndBoxesGame();
        });
    </script>
</body>
</html>
