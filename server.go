package main

import (
	"context"
	"errors"
	"fmt"
	"sync"

	gameapi "github.com/BhaumikTalwar/Amrita/service/GameApi"
)

type DotsAndBoxes struct{}

func NewGame() gameapi.Game {
	return &DotsAndBoxes{}
}

type DotsAndBoxesInstance struct {
	roomID      string
	gridSize    int
	lines       map[string]bool // Set of line IDs that have been drawn
	boxes       []Box           // All boxes in the game
	players     map[string]int  // playerID -> player number (1 or 2)
	playerNames map[int]string  // player number -> player name
	currentTurn int             // 1 or 2
	scores      map[int]int     // player number -> score
	config      gameapi.GameConfig
	mu          sync.Mutex
	isGameOver  bool
	winner      int // 0 for tie, 1 or 2 for player number
}

type Box struct {
	Row   int `json:"row"`
	Col   int `json:"col"`
	Owner int `json:"owner"` // 0 for unowned, 1 or 2 for player number
}

func (g *DotsAndBoxes) NewInstance(config gameapi.GameConfig, roomID string) gameapi.GameInstance {
	if err := g.ValidateConfig(config); err != nil {
		return nil
	}
	
	gridSize := 8 // Default grid size
	if size, ok := config["gridSize"]; ok {
		if s, ok := size.(int); ok {
			gridSize = s
		}
	}
	
	instance := &DotsAndBoxesInstance{
		roomID:      roomID,
		gridSize:    gridSize,
		lines:       make(map[string]bool),
		boxes:       make([]Box, 0),
		players:     make(map[string]int),
		playerNames: make(map[int]string),
		scores:      make(map[int]int),
		config:      config,
		isGameOver:  false,
		winner:      0,
	}
	
	// Initialize boxes
	for row := 0; row < gridSize-1; row++ {
		for col := 0; col < gridSize-1; col++ {
			instance.boxes = append(instance.boxes, Box{
				Row:   row,
				Col:   col,
				Owner: 0,
			})
		}
	}
	
	instance.scores[1] = 0
	instance.scores[2] = 0
	
	return instance
}

func (g *DotsAndBoxes) ValidateConfig(config gameapi.GameConfig) error {
	if size, ok := config["gridSize"]; ok {
		if s, ok := size.(int); !ok || s < 4 || s > 10 {
			return errors.New("gridSize must be an integer between 4 and 10")
		}
	}
	return nil
}

func (g *DotsAndBoxesInstance) HandlePlayerJoin(ctx context.Context, playerID string, playerData interface{}) error {
	g.mu.Lock()
	defer g.mu.Unlock()

	if len(g.players) >= 2 {
		return errors.New("game is full")
	}

	pData, ok := playerData.(map[string]interface{})
	if !ok {
		return errors.New("invalid player data format")
	}
	name, ok := pData["username"].(string)
	if !ok {
		return errors.New("username not provided")
	}

	playerNumber := 1
	if len(g.players) == 1 {
		// Check if player 1 is already taken
		for _, pNum := range g.players {
			if pNum == 1 {
				playerNumber = 2
				break
			}
		}
	}

	g.players[playerID] = playerNumber
	g.playerNames[playerNumber] = name

	if len(g.players) == 2 {
		g.currentTurn = 1 // Player 1 starts
	}

	return nil
}

func (g *DotsAndBoxesInstance) HandlePlayerAction(ctx context.Context, playerID string, action interface{}) error {
	g.mu.Lock()
	defer g.mu.Unlock()

	if g.isGameOver {
		return errors.New("game is over")
	}

	playerNumber, exists := g.players[playerID]
	if !exists {
		return errors.New("player not in game")
	}
	if playerNumber != g.currentTurn {
		return errors.New("not your turn")
	}

	move, ok := action.(map[string]interface{})
	if !ok {
		return errors.New("invalid action format")
	}

	startRow, ok := move["startRow"].(float64)
	if !ok {
		return errors.New("invalid startRow")
	}
	startCol, ok := move["startCol"].(float64)
	if !ok {
		return errors.New("invalid startCol")
	}
	endRow, ok := move["endRow"].(float64)
	if !ok {
		return errors.New("invalid endRow")
	}
	endCol, ok := move["endCol"].(float64)
	if !ok {
		return errors.New("invalid endCol")
	}

	sRow, sCol := int(startRow), int(startCol)
	eRow, eCol := int(endRow), int(endCol)

	// Validate the line
	if !g.isValidLine(sRow, sCol, eRow, eCol) {
		return errors.New("invalid line")
	}

	lineID := g.getLineID(sRow, sCol, eRow, eCol)
	if g.lines[lineID] {
		return errors.New("line already exists")
	}

	// Draw the line
	g.lines[lineID] = true

	// Check for completed boxes
	completedBoxes := g.checkCompletedBoxes()
	
	if len(completedBoxes) > 0 {
		// Player gets points and another turn
		g.scores[playerNumber] += len(completedBoxes)
		for _, boxIndex := range completedBoxes {
			g.boxes[boxIndex].Owner = playerNumber
		}
		// Player keeps their turn
	} else {
		// Switch turns
		if g.currentTurn == 1 {
			g.currentTurn = 2
		} else {
			g.currentTurn = 1
		}
	}

	// Check if game is over
	if g.checkGameEnd() {
		g.isGameOver = true
		if g.scores[1] > g.scores[2] {
			g.winner = 1
		} else if g.scores[2] > g.scores[1] {
			g.winner = 2
		} else {
			g.winner = 0 // Tie
		}
	}

	return nil
}

func (g *DotsAndBoxesInstance) HandlePlayerLeave(ctx context.Context, playerID string) error {
	g.mu.Lock()
	defer g.mu.Unlock()

	playerNumber, exists := g.players[playerID]
	if !exists {
		return errors.New("player not in game")
	}

	delete(g.players, playerID)
	delete(g.playerNames, playerNumber)
	g.isGameOver = true
	
	// The remaining player wins
	for _, pNum := range g.players {
		g.winner = pNum
		break
	}
	
	return nil
}

func (g *DotsAndBoxesInstance) GetGameState(playerID string) interface{} {
	g.mu.Lock()
	defer g.mu.Unlock()

	playerNumber, exists := g.players[playerID]
	if !exists {
		return map[string]interface{}{
			"error": "player not in game",
		}
	}

	return map[string]interface{}{
		"gridSize":    g.gridSize,
		"lines":       g.lines,
		"boxes":       g.boxes,
		"playerNumber": playerNumber,
		"currentTurn": g.currentTurn,
		"players":     g.playerNames,
		"scores":      g.scores,
		"isGameOver":  g.isGameOver,
		"winner":      g.winner,
	}
}

func (g *DotsAndBoxesInstance) GetPublicState() interface{} {
	g.mu.Lock()
	defer g.mu.Unlock()

	return map[string]interface{}{
		"gridSize":   g.gridSize,
		"lines":      g.lines,
		"boxes":      g.boxes,
		"currentTurn": g.currentTurn,
		"players":    g.playerNames,
		"scores":     g.scores,
		"isGameOver": g.isGameOver,
		"winner":     g.winner,
	}
}

func (g *DotsAndBoxesInstance) IsGameOver() bool {
	g.mu.Lock()
	defer g.mu.Unlock()
	return g.isGameOver
}

func (g *DotsAndBoxesInstance) GetWinner() interface{} {
	g.mu.Lock()
	defer g.mu.Unlock()
	return g.winner
}

func (g *DotsAndBoxesInstance) Cleanup() error {
	g.mu.Lock()
	defer g.mu.Unlock()

	g.lines = make(map[string]bool)
	g.boxes = make([]Box, 0)
	g.players = make(map[string]int)
	g.playerNames = make(map[int]string)
	g.scores = make(map[int]int)
	g.currentTurn = 0
	g.isGameOver = false
	g.winner = 0
	
	// Reinitialize boxes
	for row := 0; row < g.gridSize-1; row++ {
		for col := 0; col < g.gridSize-1; col++ {
			g.boxes = append(g.boxes, Box{
				Row:   row,
				Col:   col,
				Owner: 0,
			})
		}
	}
	
	g.scores[1] = 0
	g.scores[2] = 0

	return nil
}

// Helper functions for game logic

func (g *DotsAndBoxesInstance) isValidLine(startRow, startCol, endRow, endCol int) bool {
	// Check bounds
	if startRow < 0 || startRow >= g.gridSize || startCol < 0 || startCol >= g.gridSize ||
		endRow < 0 || endRow >= g.gridSize || endCol < 0 || endCol >= g.gridSize {
		return false
	}

	// Check if it's a valid adjacent connection (horizontal or vertical)
	rowDiff := abs(startRow - endRow)
	colDiff := abs(startCol - endCol)

	return (rowDiff == 1 && colDiff == 0) || (rowDiff == 0 && colDiff == 1)
}

func (g *DotsAndBoxesInstance) getLineID(startRow, startCol, endRow, endCol int) string {
	// Normalize the line ID so that the same line always has the same ID
	minRow := min(startRow, endRow)
	maxRow := max(startRow, endRow)
	minCol := min(startCol, endCol)
	maxCol := max(startCol, endCol)

	if minRow == maxRow {
		// Horizontal line
		return fmt.Sprintf("horizontal-%d-%d", minRow, minCol)
	} else {
		// Vertical line
		return fmt.Sprintf("vertical-%d-%d", minRow, minCol)
	}
}

func (g *DotsAndBoxesInstance) checkCompletedBoxes() []int {
	var completedBoxes []int

	for i, box := range g.boxes {
		if box.Owner == 0 && g.isBoxComplete(box.Row, box.Col) {
			completedBoxes = append(completedBoxes, i)
		}
	}

	return completedBoxes
}

func (g *DotsAndBoxesInstance) isBoxComplete(row, col int) bool {
	top := fmt.Sprintf("horizontal-%d-%d", row, col)
	bottom := fmt.Sprintf("horizontal-%d-%d", row+1, col)
	left := fmt.Sprintf("vertical-%d-%d", row, col)
	right := fmt.Sprintf("vertical-%d-%d", row, col+1)

	return g.lines[top] && g.lines[bottom] && g.lines[left] && g.lines[right]
}

func (g *DotsAndBoxesInstance) checkGameEnd() bool {
	totalBoxes := (g.gridSize - 1) * (g.gridSize - 1)
	completedBoxes := g.scores[1] + g.scores[2]
	return completedBoxes == totalBoxes
}

// Utility functions
func abs(x int) int {
	if x < 0 {
		return -x
	}
	return x
}

func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}

func max(a, b int) int {
	if a > b {
		return a
	}
	return b
}
